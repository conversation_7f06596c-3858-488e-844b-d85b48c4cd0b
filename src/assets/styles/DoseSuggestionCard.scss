@use '../variables' as *;

.container {
  padding: 50px 0;
}

.buttonContainer {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.expandedWrapperRed {
  background-color: $where-to-buy-BG;
  padding: 10px 0 0 0;
}

.expandedBox {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  padding: 15px;
}

.expandedText {
  color: $ypd-blue;
  font-size: 14px;
  flex: 1;
  margin: 0 10px 0 0;
}


.divider {
  height: 2px;
  background-color: $ypd-white;
  margin: 10px 0px 0px 0px;
  width: 100%;
}

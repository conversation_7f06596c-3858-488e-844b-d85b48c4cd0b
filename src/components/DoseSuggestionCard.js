import { useContext, useEffect, useState } from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { ThemeContext } from '../context/ThemeContext';
import { Fonts } from '../utils';
import CustomHomeScreenButton from './CustomHomeScreenButton';
import { ypdTeal, ypdWhite } from '../utils/colors';
import CustomButton from './CustomButton';
import styles from '../assets/styles/DoseSuggestionCard';
import Heading from './Heading';

const DoseSuggestionCard = ({ lastCheckinMap = {} }) => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const dosage = useSelector((state) => state.profile.initialDose);
  const dosageType = useSelector((state) => state.profile.dosageType);
  const cannabisType = useSelector((state) => state.profile.cannabisType);

  const [thciEnabled, setThciEnabled] = useState(true);
  const [thcoEnabled, setThcoEnabled] = useState(true);
  const [cbdiEnabled, setCbdiEnabled] = useState(true);
  const [cbdoEnabled, setCbdoEnabled] = useState(true);

  const [thciRemaining, setThciRemaining] = useState(0);
  const [thcoRemaining, setThcoRemaining] = useState(0);
  const [cbdiRemaining, setCbdiRemaining] = useState(0);
  const [cbdoRemaining, setCbdoRemaining] = useState(0);

  const [expandedBox, setExpandedBox] = useState(null);

  useEffect(() => {
    setThciEnabled(
      ['both', 'thc'].includes(cannabisType) &&
        ['both', 'inhaled'].includes(dosageType),
    );
    setThcoEnabled(
      ['both', 'thc'].includes(cannabisType) &&
        ['both', 'oral'].includes(dosageType),
    );
    setCbdiEnabled(
      ['both', 'cbd'].includes(cannabisType) &&
        ['both', 'inhaled'].includes(dosageType),
    );
    setCbdoEnabled(
      ['both', 'cbd'].includes(cannabisType) &&
        ['both', 'oral'].includes(dosageType),
    );
  }, [cannabisType, dosageType]);

  const thcoLastSuggestion = useSelector(
    (state) => state.suggestions.thcoLastSuggestion,
  );
  const thciLastSuggestion = useSelector(
    (state) => state.suggestions.thciLastSuggestion,
  );
  const cbdoLastSuggestion = useSelector(
    (state) => state.suggestions.cbdoLastSuggestion,
  );
  const cbdiLastSuggestion = useSelector(
    (state) => state.suggestions.cbdiLastSuggestion,
  );

  const formatTime = (totalSeconds) => {
    const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
    const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(
      2,
      '0',
    );
    const seconds = String(totalSeconds % 60).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  };

  const thcoValue =
    thcoRemaining > 0
      ? formatTime(thcoRemaining)
      : `${thcoLastSuggestion?.thco || dosage?.thcOral || 0} mg`;
  const thciValue =
    thciRemaining > 0
      ? formatTime(thciRemaining)
      : `${thciLastSuggestion?.thci || dosage?.thcVape || 0} puffs`;
  const cbdoValue =
    cbdoRemaining > 0
      ? formatTime(cbdoRemaining)
      : `${cbdoLastSuggestion?.cbdo || dosage?.cbdOral || 0} mg`;
  const cbdiValue =
    cbdiRemaining > 0
      ? formatTime(cbdiRemaining)
      : `${cbdiLastSuggestion?.cbdi || dosage?.cbdVape || 0} puffs`;

  const thcoTitle = thcoEnabled
    ? thcoRemaining > 0
      ? `Thc Oral - ${dosage?.thcOral} mg ${formatTime(thcoRemaining)} `
      : `Thc Oral - ${thcoValue}`
    : 'Add this option';
  const thciTitle = thciEnabled
    ? thciRemaining > 0
      ? `Thc Inhaled - ${dosage?.thcVape} puff(s) ${formatTime(thciRemaining)}`
      : `Thc Inhaled - ${thciValue}`
    : 'Add this option';
  const cbdoTitle = cbdoEnabled
    ? cbdoRemaining > 0
      ? `Cbd Oral - ${dosage?.cbdOral} mg ${formatTime(cbdoRemaining)}`
      : `Cbd Oral - ${cbdoValue}`
    : 'Add this option';
  const cbdiTitle = cbdiEnabled
    ? cbdiRemaining > 0
      ? `Cbd Inhaled - ${dosage?.cbdVape} mg ${formatTime(cbdiRemaining)}`
      : `Cbd Inhaled - ${cbdiValue}`
    : 'Add this option';

  const addOption = () => {
    if (dosageType !== 'both') {
      navigation.navigate('Step7', {
        profileBuilding: false,
        marginTop: 50,
      });
    } else {
      navigation.navigate('Step6', {
        profileBuilding: false,
        marginTop: 50,
      });
    }
  };

  const handleDosePress = (doseType, modality) => {
    navigation.navigate('CalendarModal', {
      modality,
      cannabisType: modality.startsWith('thc') ? 'THC' : 'CBD',
      dosageType: doseType,
      lockoutHours: LOCKOUT_IN_HOURS[modality],
    });
  };

  const LOCKOUT_IN_HOURS = {
    thci: 0.01,
    cbdi: 0.01,
    thco: 0.01,
    cbdo: 0.01,
  };

  const buildModalParams = (modality, doseType) => ({
    modality,
    cannabisType: modality.startsWith('thc') ? 'THC' : 'CBD',
    dosageType: doseType,
    lockoutHours: LOCKOUT_IN_HOURS[modality],
  });

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();

      const calcRemaining = (lastSuggestion, lockoutHours) => {
        if (!lastSuggestion) {
          return 0;
        }

        const suggestionTime = new Date(lastSuggestion);
        if (isNaN(suggestionTime.getTime())) {
          return 0;
        }
        const elapsedMs = now - suggestionTime;
        const lockoutMs = lockoutHours * 60 * 60 * 1000;
        const remainingMs = lockoutMs - elapsedMs;
        return Math.max(0, Math.floor(remainingMs / 1000));
      };

      setThciRemaining(
        thciEnabled
          ? calcRemaining(lastCheckinMap['thci'], LOCKOUT_IN_HOURS.thci)
          : 0,
      );
      setThcoRemaining(
        thcoEnabled
          ? calcRemaining(lastCheckinMap['thco'], LOCKOUT_IN_HOURS.thco)
          : 0,
      );
      setCbdiRemaining(
        cbdiEnabled
          ? calcRemaining(lastCheckinMap['cbdi'], LOCKOUT_IN_HOURS.cbdi)
          : 0,
      );
      setCbdoRemaining(
        cbdoEnabled
          ? calcRemaining(lastCheckinMap['cbdo'], LOCKOUT_IN_HOURS.cbdo)
          : 0,
      );
    }, 1000);

    return () => clearInterval(interval);
  }, [
    thciLastSuggestion,
    thcoLastSuggestion,
    cbdiLastSuggestion,
    cbdoLastSuggestion,
    lastCheckinMap,
    thciEnabled,
    thcoEnabled,
    cbdiEnabled,
    cbdoEnabled,
  ]);

  const renderExpandedBox = (modality) => (
    <>
      <View style={styles.expandedBox}>
        <Heading
          text={`Take 1 capsule by mouth \nEvery 5 hours as needed`}
          color={theme.colors.text}
          style={styles.expandedText}
        />

        <CustomButton
          title="Log as Taken"
          variant="green"
          width={150}
          height={40}
          borderRadius={20}
          fontWeight="bold"
          textColor={ypdTeal}
          ioniconName="arrow-forward"
          iconColor={ypdTeal}
          flexDirection="row-reverse"
          fontSize={14}
          size={20}
          onPress={() =>
            handleDosePress(
              modality.includes('i') ? 'inhaled' : 'oral',
              modality,
            )
          }
        />
      </View>
      <View style={styles.divider} />
    </>
  );

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <View style={styles.buttonContainer}>
        <View style={[expandedBox === 'thco' && styles.expandedWrapperRed]}>
          <CustomHomeScreenButton
            title={thcoTitle}
            dosage={null}
            onPress={() =>
              thcoEnabled && thcoRemaining === 0
                ? setExpandedBox(expandedBox === 'thco' ? null : 'thco')
                : addOption()
            }
            iconName={
              thcoEnabled && thcoRemaining === 0
                ? expandedBox === 'thco'
                  ? null
                  : 'add'
                : null
            }
            textColor={
              expandedBox === 'thco' && !theme.dark
                ? ypdWhite
                : theme.colors.text
            }
            fontSize={15}
            fontFamily={Fonts.MEDIUM}
            disabled={thcoEnabled && thcoRemaining > 0}
            style={{ backgroundColor: 'transparent' }}
            alignSelf="stretch"
            showDivider={expandedBox === 'thco' ? false : true}
            modalParams={buildModalParams('thco', 'oral')}
          />
          {expandedBox === 'thco' && renderExpandedBox('thco')}
        </View>

        <View style={[expandedBox === 'thci' && styles.expandedWrapperRed]}>
          <CustomHomeScreenButton
            title={thciTitle}
            dosage={null}
            onPress={() =>
              thciEnabled && thciRemaining === 0
                ? setExpandedBox(expandedBox === 'thci' ? null : 'thci')
                : addOption()
            }
            iconName={
              thciEnabled && thciRemaining === 0
                ? expandedBox === 'thci'
                  ? null
                  : 'add'
                : null
            }
            textColor={
              expandedBox === 'thci' && !theme.dark
                ? ypdWhite
                : theme.colors.text
            }
            fontSize={15}
            fontFamily={Fonts.MEDIUM}
            disabled={thciEnabled && thciRemaining > 0}
            style={{ backgroundColor: 'transparent' }}
            alignSelf="stretch"
            showDivider={expandedBox === 'thci' ? false : true}
            modalParams={buildModalParams('thci', 'inhaled')}
          />
          {expandedBox === 'thci' && renderExpandedBox('thci')}
        </View>

        <View style={[expandedBox === 'cbdo' && styles.expandedWrapperRed]}>
          <CustomHomeScreenButton
            title={cbdoTitle}
            dosage={null}
            onPress={() =>
              cbdoEnabled && cbdoRemaining === 0
                ? setExpandedBox(expandedBox === 'cbdo' ? null : 'cbdo')
                : addOption()
            }
            iconName={
              cbdoEnabled && cbdoRemaining === 0
                ? expandedBox === 'cbdo'
                  ? null
                  : 'add'
                : null
            }
            textColor={
              expandedBox === 'cbdo' && !theme.dark
                ? ypdWhite
                : theme.colors.text
            }
            fontSize={15}
            fontFamily={Fonts.MEDIUM}
            disabled={cbdoEnabled && cbdoRemaining > 0}
            style={{ backgroundColor: 'transparent' }}
            alignSelf="stretch"
            showDivider={expandedBox === 'cbdo' ? false : true}
            modalParams={buildModalParams('cbdo', 'oral')}
          />
          {expandedBox === 'cbdo' && renderExpandedBox('cbdo')}
        </View>

        <View style={[expandedBox === 'cbdi' && styles.expandedWrapperRed]}>
          <CustomHomeScreenButton
            title={cbdiTitle}
            dosage={null}
            onPress={() =>
              cbdiEnabled && cbdiRemaining === 0
                ? setExpandedBox(expandedBox === 'cbdi' ? null : 'cbdi')
                : addOption()
            }
            iconName={
              cbdiEnabled && cbdiRemaining === 0
                ? expandedBox === 'cbdi'
                  ? null
                  : 'add'
                : null
            }
            textColor={
              expandedBox === 'cbdi' && !theme.dark
                ? ypdWhite
                : theme.colors.text
            }
            fontSize={15}
            fontFamily={Fonts.MEDIUM}
            disabled={cbdiEnabled && cbdiRemaining > 0}
            style={{ backgroundColor: 'transparent' }}
            alignSelf="stretch"
            showDivider={false}
            modalParams={buildModalParams('cbdi', 'inhaled')}
          />
          {expandedBox === 'cbdi' && renderExpandedBox('cbdi')}
        </View>
      </View>
    </View>
  );
};

export default DoseSuggestionCard;

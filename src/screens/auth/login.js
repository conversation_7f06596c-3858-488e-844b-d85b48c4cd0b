import { useCallback, useContext, useEffect, useState } from 'react';
import {
  View,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
} from 'react-native';
import styles from '../../assets/styles/SignUp';
import {
  signInWithRedirect,
  signIn,
  getCurrentUser,
  fetchUserAttributes,
} from 'aws-amplify/auth';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { parseAWSAuthError, validateInputs } from '../../utils/utils';
import { Fonts, isSmallDevice, useEmail, useName } from '../../utils';
import { ypdRed } from '../../utils/colors';
import CustomButton from '../../components/CustomButton';
import Heading from '../../components/Heading';
import Input from '../../components/Input';
import {
  setProfileNotCreated,
  setUsername,
  updateProfile,
  setEmailAddress,
  setUserId,
} from '../../redux/slices/profileSlice';
import { useDispatch } from 'react-redux';
import { Hub } from '@aws-amplify/core';
import Apple from '../../assets/svgs/profileBuilder/Apple';
import Google from '../../assets/svgs/profileBuilder/Google';
import AuthWarning from '../../components/AuthWarnings';
import { getUserProfile } from '../../api/profile';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const Login = () => {
  const name = useName();
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const { emailFromSignup } = route.params || {};

  const [email, setEmail] = useState(useEmail());
  const [screenTime, setScreenTime] = useState(null);
  const [password, setPassword] = useState('');
  const [focusedInput, setFocusedInput] = useState(null);
  const [warningMessage, setWarningMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoginSubmitting, setIsLoginSubmitting] = useState(false);
  const [isAppleSubmitting, setIsAppleSubmitting] = useState(false);
  const [isGoogleSubmitting, setIsGoogleSubmitting] = useState(false);

  const isAnySubmitting =
    isLoginSubmitting || isGoogleSubmitting || isAppleSubmitting;

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveAuthEvent = async (username) => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'login',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Auth Event',
      username,
      true,
      '#A6',
      attributes,
      createdAt,
    );
  };

  useEffect(() => {
    let timeout;

    const unsubscribe = Hub.listen('auth', ({ payload }) => {
      switch (payload.event) {
        case 'signInWithRedirect':
          getUser();
          break;
        case 'signInWithRedirect_failure':
          setWarningMessage('Social sign-in failed. Please try again.');
          setIsGoogleSubmitting(false);
          setIsAppleSubmitting(false);
          setIsLoginSubmitting(false);
          break;
        case 'customOAuthState':
          console.log(payload.data);
          break;
      }
    });

    return () => {
      clearTimeout(timeout);
      unsubscribe();
    };
  }, []);

  const getUser = async () => {
    try {
      const { userId, username } = await getCurrentUser();
      const { email } = await fetchUserAttributes();
      const profileData = await getUserProfile(userId);
      if (profileData) {
        dispatch(updateProfile(profileData));
        dispatch(setProfileNotCreated(false));
        await saveAuthEvent(profileData.username);
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'Tabs',
              state: {
                routes: [{ name: 'HomeScreen' }],
              },
            },
          ],
        });
      } else {
        dispatch(setProfileNotCreated(true));
        dispatch(setUserId(userId));
        dispatch(setUsername(username));
        dispatch(setEmailAddress(email));
        setWarningMessage(
          'Profile not created yet. please create your profile first.',
        );
        setTimeout(() => {
          setWarningMessage('');
          navigation.navigate('Step1');
        }, 3000);
      }
    } catch (error) {
      await handleLoginError(error);
    } finally {
      setIsGoogleSubmitting(false);
      setIsAppleSubmitting(false);
      setIsLoginSubmitting(false);
    }
  };

  const handleLogin = async () => {
    if (isAnySubmitting) return;
    const error = validateInputs(email, true, password, true);
    if (error) {
      setWarningMessage(error);
      return;
    }
    setIsLoginSubmitting(true);
    try {
      const response = await signIn({ username: email, password });

      if (response['nextStep']['signInStep'] === 'CONFIRM_SIGN_UP') {
        dispatch(setUsername(email));
        await saveAuthEvent(email);
        navigation.navigate('ConfirmSignUp', { password });
      } else {
        await getUser();
      }
    } catch (error) {
      await handleLoginError(error);
    } finally {
      setIsLoginSubmitting(false);
    }
  };

  const handleProvidersSignup = async (provider) => {
    if (isAnySubmitting) return;
    if (provider === 'Apple') setIsAppleSubmitting(true);
    if (provider === 'Google') setIsGoogleSubmitting(true);

    const timeout = setTimeout(() => {
      if (provider === 'Apple') setIsAppleSubmitting(false);
      if (provider === 'Google') setIsGoogleSubmitting(false);
    }, 20000);

    try {
      await signInWithRedirect({ provider });
    } catch (error) {
      clearTimeout(timeout);
      await handleLoginError(error);
    }
  };

  const handleLoginError = async (error) => {
    console.log('Error during login:', error);
    let errorMessage = parseAWSAuthError(error);

    if (errorMessage === 'There is already a signed in user.') {
      await getUser();
      return;
    }

    if (errorMessage === 'PreSignUp failed with error Email already in use.') {
      errorMessage = 'Email already in use.';
    }
    setWarningMessage(errorMessage);
    setIsLoginSubmitting(false);
    setIsGoogleSubmitting(false);
    setIsAppleSubmitting(false);
  };

  return (
    <>
      {warningMessage && (
        <AuthWarning
          text={warningMessage}
          icon="alert-outline"
          size={30}
          iconColor={ypdRed}
          showCloseButton={true}
          onClose={() => setWarningMessage('')}
        />
      )}
      <SafeAreaView
        style={{ flex: 1, backgroundColor: theme.colors.background }}
      >
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
          <TouchableWithoutFeedback
            onPress={Keyboard.dismiss}
            accessible={false}
          >
            <View style={styles.container}>
              <Heading
                text={name ? `${name}, welcome back!` : 'Welcome back!'}
                size="lg"
                fontFamily={Fonts.MEDIUM}
                color={
                  focusedInput === 'email'
                    ? theme.colors.text
                    : theme.colors.textSecondary
                }
                style={isSmallDevice ? styles.headingSmall : styles.heading}
              />
              <View style={styles.wrapper}>
                <View style={styles.inputContainer}>
                  <Heading
                    text="Email"
                    fontSize={12}
                    style={styles.inputHeading}
                    color={theme.colors.text}
                  />
                  <Input
                    placeholder="Enter your email"
                    value={email || emailFromSignup}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    onFocus={() => setFocusedInput('email')}
                    onBlur={() => setFocusedInput(null)}
                    autoCapitalize="none"
                    autoCorrect={false}
                    accessibilityLabel="Email input"
                    disabled={isAnySubmitting}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Heading
                    text="Password"
                    fontSize={12}
                    style={styles.inputHeading}
                    color={theme.colors.text}
                  />
                  <Input
                    placeholder="Enter your password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!showPassword}
                    onFocus={() => setFocusedInput('password')}
                    onBlur={() => setFocusedInput(null)}
                    accessibilityLabel="Password input"
                    showIcon={true}
                    iconName={showPassword ? 'eye' : 'eye-off'}
                    onIconPress={() => setShowPassword(!showPassword)}
                    disabled={isAnySubmitting}
                    style={[{ color: theme.colors.text }]}
                  />
                  <Heading
                    text="I forgot my password"
                    size="link"
                    style={[
                      styles.forgotPassword,
                      { color: theme.colors.linkText },
                    ]}
                    onPress={() => navigation.navigate('ResetPassword')}
                  />
                </View>
                <View style={styles.buttonContainer}>
                  <CustomButton
                    title="Log in"
                    onPress={handleLogin}
                    variant="green"
                    width="100%"
                    disabled={isAnySubmitting}
                    activityIndicator={isLoginSubmitting}
                  />
                </View>
                <View style={styles.dividerContainer}>
                  <View style={styles.divider} />
                  <Heading
                    text="or"
                    size="xsSmall"
                    style={styles.dividerText}
                    color={theme.colors.text}
                  />
                  <View style={styles.divider} />
                </View>
                <CustomButton
                  title="Continue with Google"
                  width="100%"
                  variant="lightoutline"
                  onPress={() => handleProvidersSignup('Google')}
                  fontSize={14}
                  size={20}
                  textColor={theme.colors.text}
                  svgIcon={<Google />}
                  style={[styles.googleButton, { color: theme.colors.text }]}
                  disabled={isAnySubmitting}
                  activityIndicator={isGoogleSubmitting}
                />
                <CustomButton
                  title="Continue with Apple"
                  width="100%"
                  variant="lightoutline"
                  onPress={() => handleProvidersSignup('Apple')}
                  fontSize={14}
                  size={20}
                  textColor={theme.colors.text}
                  svgIcon={<Apple />}
                  disabled={isAnySubmitting}
                  activityIndicator={isAppleSubmitting}
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </>
  );
};

export default Login;

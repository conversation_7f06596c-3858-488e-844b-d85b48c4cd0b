import { useDispatch, useSelector } from 'react-redux';
import { useCallback, useContext, useState } from 'react';
import { View, SafeAreaView, Platform } from 'react-native';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import ProfileBuilderButton from '../../components/ProfileBuilderButton';
import { resetCheckinState } from '../../redux/slices/checkinSlice';
import SuggestionPopup from '../../components/SuggestionPopup';
import { createDoseSuggestion } from '../../api/suggestion';
import { ThemeContext } from '../../context/ThemeContext';
import CustomButton from '../../components/CustomButton';
import Heading from '../../components/Heading';
import styles from '../../assets/styles/SaveDose';
import { recordEvent } from '../../api/events';
import { Fonts } from '../../utils';

const SaveDose = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const { suggestionData = {} } = route.params || {};

  const [screenTime, setScreenTime] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showTimeLockoutPopup, setShowTimeLockoutPopup] = useState(false);

  const profile = useSelector((state) => state.profile);
  const allowCheckin = useSelector((state) => state.suggestions.allowCheckin);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveDosingEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      isCompleted: true,
      currentScreen: 'journal',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Dosing Event',
      profile.username,
      true,
      '#D6',
      attributes,
      createdAt,
    );
  };

  const saveCheckIn = async (newSuggestion = false) => {
    setLoading(true);
    suggestionData['suggestionType'] = newSuggestion ? 'regular' : 'journal';
    const params = newSuggestion ? { newSuggestion: true } : { newNote: true };
    dispatch(resetCheckinState());
    await createDoseSuggestion(suggestionData);
    await new Promise((resolve) => setTimeout(resolve, 3000));
    await saveDosingEvent();
    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'Tabs',
          state: {
            index: 0,
            routes: [
              {
                name: 'Home',
                params: params,
              },
            ],
          },
        },
      ],
    });

    setLoading(false);
  };

  const getNewSuggestion = async () => {
    if (allowCheckin) {
      await saveCheckIn(true);
    } else {
      setShowTimeLockoutPopup(true);
    }
  };

  const handleTimeLockoutPopupClose = () => {
    setShowTimeLockoutPopup(false);
  };

  return (
    <SafeAreaView
      style={[styles.safeArea, { backgroundColor: theme.colors.background }]}
    >
      <View style={styles.container}>
        <View style={styles.headerIcons}>
          <Heading
            text="Thanks for your input. What would you like to do next?"
            size="lg"
            fontFamily={Fonts.MEDIUM}
            color={theme.colors.text}
          />
        </View>
        <View style={styles.buttonsContainer}>
          <View style={styles.buttonsRow}>
            <CustomButton
              width={150}
              height={50}
              variant="newGreen"
              title="Save in journal"
              disabled={loading}
              onPress={() => {
                saveCheckIn(false);
              }}
              activityIndicator={loading}
            />
            <CustomButton
              title="Get a new suggestion"
              onPress={getNewSuggestion}
              disabled={loading}
              variant="newGreen"
              width={185}
              height={50}
            />
          </View>
        </View>
      </View>
      <View
        style={[
          styles.backButtonContainer,
          Platform.OS === 'android' && styles.backButtonContainerAndroid,
        ]}
      >
        <ProfileBuilderButton
          continueText="Next"
          onlyBackButton={true}
          backDisabled={loading}
          activityIndicator={loading}
          onBackPress={() => navigation.goBack()}
        />
      </View>
      <SuggestionPopup
        visible={showTimeLockoutPopup}
        onClose={handleTimeLockoutPopupClose}
        data={[
          'We should wait for an hour or two to let the current suggestion take full effect.',
          'Please come back later, then you can get a new suggestion',
        ]}
        isClose={true}
      />
    </SafeAreaView>
  );
};

export default SaveDose;

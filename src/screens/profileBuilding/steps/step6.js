import { useCallback, useState, useEffect, useContext } from 'react';
import {
  View,
  SafeAreaView,
  TouchableOpacity,
  Linking,
  BackHandler,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CustomButton from '../../../components/CustomButton';
import Heading from '../../../components/Heading';
import { Fonts, responsiveStyles } from '../../../utils';
import styles from '../../../assets/styles/ProfileBuilderStyles';
import { useDispatch, useSelector } from 'react-redux';
import { setCannabisType } from '../../../redux/slices/profileSlice';
import ProgressBar from '../progressBar';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { ypdWhite } from '../../../utils/colors';
import ProfileBuilderButton from '../../../components/ProfileBuilderButton';
import Pills from '../../../assets/svgs/profileBuilder/Pills';
import Justice from '../../../assets/svgs/profileBuilder/Justice';
import { resetLoadingState } from '../../../utils';
import { updateUserProfile } from '../../../api/profile';
import { recordEvent } from '../../../api/events';
import { ThemeContext } from '../../../context/ThemeContext';
import BottomSheet from '../../../components/BottomSheet';

const Step6 = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const { profileBuilding = true, marginTop = 0 } = route.params || {};

  const [screenTime, setScreenTime] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const profileData = useSelector((state) => state.profile);
  const cannabisType = useSelector((state) => state.profile.cannabisType);
  const [oldCannabisType] = useState(cannabisType || 'both');

  resetLoadingState(setIsLoading);

  useEffect(() => {
    const backAction = () => {
      if (cannabisType === '' || isLoading) {
        return true;
      }
      dispatch(setCannabisType(oldCannabisType));
      navigation.goBack();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [isLoading, dispatch, navigation, oldCannabisType]);

  const toggleState = (e, state) => {
    e.preventDefault();
    dispatch(setCannabisType(state));
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  const handleNextPress = async () => {
    const createdAt = new Date().toISOString();
    setIsLoading(true);
    if (!profileBuilding) {
      try {
        await updateUserProfile(profileData);
        const attributes = {
          currentScreen: 'cannabisType',
          screenTime: (new Date() - screenTime) / 1000,
        };
        await recordEvent(
          'Profile Event',
          profileData.username,
          true,
          '#P2',
          attributes,
          createdAt,
        );
        navigation.navigate('Tabs', { screen: 'Home' });
      } catch (error) {
        console.error('Error updating profile:', error);
      } finally {
        setIsLoading(false);
      }
    } else {
      const attributes = {
        currentScreen: 'cannabisType',
        screenTime: (new Date() - screenTime) / 1000,
      };
      await recordEvent(
        'Profile Building Event',
        profileData.username,
        true,
        '#PB8',
        attributes,
        createdAt,
      );
      navigation.navigate('Step7', { profileBuilding: true });
    }
  };

  const handleBackPress = () => {
    dispatch(setCannabisType(oldCannabisType));
    navigation.goBack();
  };

  const handleReadMorePress = () => {
    Linking.openURL('https://yourperfectdose.com/guide-page').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaView
        style={[
          styles.profileBuilderContainer,
          { backgroundColor: theme.colors.background },
        ]}
      >
        <View style={{ flex: 1 }}>
          <View style={[styles.stickyWrapper, { marginTop }]}>
            <View
              style={[
                styles.navigationButtonPosition,
                {
                  top: profileBuilding
                    ? responsiveStyles.buttonPosition
                    : responsiveStyles.buttonPosition - 80,
                },
              ]}
            ></View>
            <View style={{ flex: 1 }}>
              {profileBuilding && (
                <View style={styles.progressBar}>
                  <ProgressBar step={6} />
                </View>
              )}
              <ScrollView
                keyboardShouldPersistTaps="handled"
                contentContainerStyle={[
                  profileBuilding
                    ? { paddingTop: responsiveStyles.progressBarPaddingTop }
                    : { paddingTop: 20 },
                ]}
              >
                <View style={styles.profileBuilderScrollContainer}>
                  <View style={styles.container}>
                    <View>
                      <View>
                        <Heading
                          text="Select THC, CBD, or both to get started."
                          size="lg"
                          fontFamily={Fonts.MEDIUM}
                          color={theme.colors.text}
                        />
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            marginTop: 130,
                          }}
                          pointerEvents={isLoading ? 'none' : 'auto'}
                        >
                          <CustomButton
                            title="Both"
                            width="30%"
                            height="42"
                            borderRadius={8}
                            variant={
                              cannabisType === 'both'
                                ? 'stepsgreenoutline'
                                : 'outline'
                            }
                            fontFamily={Fonts.MEDIUM}
                            color={
                              cannabisType === 'both'
                                ? theme.colors.text
                                : theme.colors.textSecondary
                            }
                            onPress={(e) => toggleState(e, 'both')}
                          />
                          <CustomButton
                            title="THC"
                            width="30%"
                            height="42"
                            borderRadius={8}
                            variant={
                              cannabisType === 'thc'
                                ? 'stepsgreenoutline'
                                : 'outline'
                            }
                            fontFamily={Fonts.MEDIUM}
                            color={
                              cannabisType === 'thc'
                                ? theme.colors.text
                                : theme.colors.textSecondary
                            }
                            onPress={(e) => toggleState(e, 'thc')}
                          />
                          <CustomButton
                            title="CBD"
                            width="30%"
                            height="42"
                            borderRadius={8}
                            variant={
                              cannabisType === 'cbd'
                                ? 'stepsgreenoutline'
                                : 'outline'
                            }
                            fontFamily={Fonts.MEDIUM}
                            color={
                              cannabisType === 'cbd'
                                ? theme.colors.text
                                : theme.colors.textSecondary
                            }
                            onPress={(e) => toggleState(e, 'cbd')}
                          />
                        </View>
                      </View>
                    </View>

                    <TouchableOpacity
                      onPress={toggleDetails}
                      style={styles.toggleContainer}
                      disabled={isLoading}
                    >
                      <Heading
                        text="See why we suggest THC + CBD"
                        size="sm"
                        fontFamily={Fonts.MEDIUM}
                        color={theme.colors.link}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </ScrollView>
              <View style={[styles.bottomButton]}>
                <ProfileBuilderButton
                  continueText="Next"
                  onContinuePress={handleNextPress}
                  continueDisabled={isLoading}
                  activityIndicator={isLoading}
                  backDisabled={isLoading}
                  onBackPress={handleBackPress}
                  onlyNextButton={profileBuilding}
                />
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>

      <BottomSheet visible={showDetails} onClose={() => setShowDetails(false)}>
        <ScrollView style={styles.bottomSheetContainer}>
          <View style={{ height: 10 }} />
          <View
            style={[
              styles.cardContainer,
              { backgroundColor: theme.colors.lightTextGray },
              { borderColor: theme.colors.stepCards },
              { borderWidth: 1 },
            ]}
          >
            <Heading
              text="A powerful duo:"
              size="md"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <Heading
              text="CBD is the background relief that THC builds on to get you better. Together they are a balanced toolkit you can use to get the specific relief you want. YPD will help you with this real time balancing."
              fontFamily={Fonts.MEDIUM}
              style={{ lineHeight: 20 }}
              fontSize={16}
              color={theme.colors.text}
            />
          </View>

          <View style={styles.imageContainer}>
            <Justice style={styles.imageJusticeWrapper} />
          </View>

          <View style={styles.smallDescriptionContainer}>
            <Heading
              text="CBD vs. THC: Understanding the Differences and Benefits"
              size="md"
              fontFamily={Fonts.REGULAR}
              fontSize={20}
              color={theme.colors.text}
            />
          </View>

          <View style={styles.headerWrapper}>
            <View style={styles.left}>
              <Heading
                text="CBD"
                size="md"
                fontFamily={Fonts.MEDIUM}
                color={theme.colors.textBlack}
              />
            </View>
            <View style={styles.right}>
              <Heading
                text="THC"
                size="md"
                fontFamily={Fonts.MEDIUM}
                color={theme.colors.textBlack}
              />
            </View>
          </View>
          <View style={styles.contentWrapper}>
            <View style={styles.textContainerTHC}>
              <Heading
                text="Cannabidiol, has shown solid benefits that help users decrease their mental stress, anxiety and pain. It sets the stage for better sleep when combined with THC. CBD also cuts any high users may get when they use THC. CBD provides a solid foundation of relief that THC can then build on to get you better."
                size="sm"
                fontFamily={Fonts.REGULAR}
                fontSize={14}
                color={theme.colors.text}
              />
            </View>
            <View style={styles.textContainerCBD}>
              <Heading
                text="Tetrahydrocannibinol, works directly to help decrease mental stress, anxiety, and pain.  At night, it helps users get to sleep and stay asleep.  The amount of THC needed for this relief is usually way less than the amount used for recreation. Any high felt can be alleviated by using CBD with the THC."
                size="sm"
                fontFamily={Fonts.REGULAR}
                fontSize={14}
                color={theme.colors.text}
              />
            </View>
          </View>

          <View style={styles.imageContainer}>
            <Pills style={styles.image} />
          </View>

          <View style={styles.cardQuestionContainer}>
            <Heading
              text={
                'Q: Will I get high? \nThis is unlikely because medical doses of THC are lower than recreational doses. Using CBD with THC can decrease any buzz.'
              }
              size="sm"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <Heading
              text={
                'Q: Will I get red eyes?\nMedical cannabis users usually do not. '
              }
              size="sm"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
            <Heading
              text={
                'Q: Will I smell like marijuana?\nNo, if you use oral and vaped cannabis. We think it is way healthier not to smoke cannabis.'
              }
              size="sm"
              fontFamily={Fonts.MEDIUM}
              color={theme.colors.text}
            />
          </View>

          <View style={styles.customBottomButton}>
            <View pointerEvents={isLoading ? 'none' : 'auto'}>
              <CustomButton
                title="Read more about CBD and THC"
                variant="teal"
                alignSelf="center"
                width="95%"
                textStyle={{ color: ypdWhite }}
                color={ypdWhite}
                onPress={handleReadMorePress}
              />
            </View>
          </View>
        </ScrollView>
      </BottomSheet>
    </GestureHandlerRootView>
  );
};

export default Step6;

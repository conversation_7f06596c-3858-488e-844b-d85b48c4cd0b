import {
  ActivityIndicator,
  Linking,
  SafeAreaView,
  ScrollView,
  View,
  TouchableOpacity,
} from 'react-native';
import Heading from '../../components/Heading';
import SuggestionPopup from '../../components/SuggestionPopup';
import styles from '../../assets/styles/Settings.scss';
import ProfileListRender from '../../components/ProfileListRender';
import { Fonts, resetState } from '../../utils';
import MyProfileBanner from '../../assets/svgs/myProfile/MyProfileBanner';
import { CommonActions, useNavigation } from '@react-navigation/native';
import { useState, useContext, useEffect } from 'react';
import { signOut } from 'aws-amplify/auth';
import { ypdGreen, ypdOldGreen, ypdWhite } from '../../utils/colors';
import { ThemeContext } from '../../context/ThemeContext';
import RatingModal from '../../components/RatingModal';
import {
  checkRatingModalAgainstLastShown,
  checkRatingModalAgainstValue,
} from '../../utils/ratings';
import { useDispatch, useSelector } from 'react-redux';
import { updateUserDetails } from '../../api/profile';
import { setDarkModeModalIntroduced } from '../../redux/slices/profileSlice';
import FeedbackBottomSheet from '../../components/FeeedbackBottomSheet';

const Settings = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [loading, setLoading] = useState(false);
  const [ratingModalVisible, setRatingModalVisible] = useState(false);
  const [ratingButtonVisible, setRatingButtonVisible] = useState(false);

  const userId = useSelector((state) => state.profile.userId);
  const darkModeModalIntroduced = useSelector(
    (state) => state.profile.darkModeModalIntroduced,
  );

  useEffect(() => {
    const checkRatingModalVisibility = async () => {
      const canRate = await checkRatingModalAgainstValue(userId);
      const showModal = await checkRatingModalAgainstLastShown(userId);
      setRatingModalVisible(showModal);
      setRatingButtonVisible(canRate);
    };
    checkRatingModalVisibility();
  }, []);

  const handleNotifications = () => {
    navigation.navigate('Notifications');
  };

  const handleManageAccount = () => {
    navigation.navigate('ManageAccount');
  };

  const handleFAQ = () => {
    Linking.openURL('https://yourperfectdose.com/faq-page').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  const handleGuides = () => {
    Linking.openURL('https://yourperfectdose.com/guide-page').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  const handleReports = () => {
    navigation.navigate('Reports');
  };

  const handleShareApp = () => {
    navigation.navigate('ShareApp');
  };

  const handleContactUs = () => {
    Linking.openURL('https://yourperfectdose.com/contact-us').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  const handleBug = () => {
    console.log('Report a Bug Pressed');
  };

  const handleRating = () => {
    setRatingModalVisible(true);
    updateUserDetails({
      userId: userId,
      ratingModalShownAt: new Date(),
    });
  };

  const handleDarkModeModal = () => {
    dispatch(setDarkModeModalIntroduced(true));
    updateUserDetails({
      userId: userId,
      darkModeModalIntroduced: true,
    });
  };

  const handleAboutUs = () => {
    navigation.navigate('AboutUs');
  };

  const handleGoals = () => {
    navigation.navigate('Step4', {
      profileBuilding: false,
    });
  };

  const handleCannabisType = () => {
    navigation.navigate('Step6', {
      profileBuilding: false,
      marginTop: 50,
    });
  };

  const handleDosageType = () => {
    navigation.navigate('Step7', {
      profileBuilding: false,
      marginTop: 50,
    });
  };

  const handleSharingPress = () => {
    Linking.openURL('https://www.yourperfectdose.com/sharing').catch((err) =>
      console.error('Failed to open URL', err),
    );
  };

  const handleLogout = async () => {
    try {
      setLoading(true);
      await resetState(signOut);
      console.log('Logout successful.');
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'MainIntro' }],
        }),
      );
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      setLoading(false);
    }
  };

  const optionsList = [
    {
      heading: 'Preferences',
      buttons: [
        {
          title: 'Appearance',
          onPress: () => navigation.navigate('Appearance'),
          showIcon: true,
        },
        {
          title: 'Notifications',
          onPress: handleNotifications,
          showIcon: true,
        },
        { title: 'Dosage Type', onPress: handleDosageType, showIcon: true },
        { title: 'Cannabis Type', onPress: handleCannabisType, showIcon: true },
        { title: 'My Goals', onPress: handleGoals, showIcon: true },
        {
          title: 'Manage Account (email/age/sex)',
          onPress: handleManageAccount,
          showIcon: true,
        },
      ],
    },
    {
      heading: 'General',
      buttons: [
        { title: 'FAQ', onPress: handleFAQ, showIcon: true },
        { title: 'Guides', onPress: handleGuides, showIcon: true },
        { title: 'Share App', onPress: handleShareApp, showIcon: true },
        { title: 'User Reports', onPress: handleReports, showIcon: true },
      ],
    },
    {
      heading: 'Support',
      buttons: [
        { title: 'Contact us', onPress: handleContactUs, showIcon: true },
        // { title: 'Report a bug', onPress: handleBug, showIcon: true },
        { title: 'About Us', onPress: handleAboutUs, showIcon: true },
        ...(ratingButtonVisible
          ? [{ title: 'Rate us', onPress: handleRating, showIcon: false }]
          : []),
        { title: 'Logout', onPress: handleLogout, showIcon: false },
      ],
    },
  ];

  return (
    <SafeAreaView
      style={[
        styles.safeAreaView,
        { backgroundColor: theme.colors.background },
      ]}
    >
      <ScrollView>
        <View style={styles.screen}>
          <View style={styles.headerContainer}>
            <Heading
              text="Settings"
              fontSize={24}
              fontFamily={Fonts.REGULAR}
              color={theme.colors.text}
            />
          </View>

          <View style={styles.shareContainer}>
            <View style={styles.leftContent}>
              <Heading
                text="Sharing is caring"
                fontSize={20}
                fontFamily={Fonts.MEDIUM}
                color={theme.colors.ypdPrimary}
              />

              <Heading
                text="Help your friends find their perfect dose and feel like themselves again"
                fontSize={13}
                fontFamily={Fonts.REGULAR}
                color={ypdWhite}
              />

              <TouchableOpacity onPress={handleSharingPress}>
                <Heading
                  text="Sharable link"
                  style={[
                    styles.shareLink,
                    {
                      textDecorationLine: 'underline',
                      textDecorationColor: ypdGreen,
                    },
                  ]}
                  fontSize={14}
                  fontFamily={Fonts.REGULAR}
                  color={theme.colors.ypdPrimary}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.rightContent}>
              <MyProfileBanner />
            </View>
          </View>

          <View
            style={[
              styles.buttonContainer,
              { backgroundColor: theme.colors.background },
            ]}
          >
            {optionsList.map((section, index) => (
              <View key={index}>
                <ProfileListRender
                  text={section.heading}
                  listOfButtons={section.buttons}
                  padding={[0, 0, 0, 10]}
                  textColor={theme.colors.text}
                />
                <View
                  style={[
                    styles.divider,
                    { backgroundColor: theme.colors.border },
                  ]}
                />
              </View>
            ))}
          </View>
        </View>
        <RatingModal
          visible={ratingModalVisible}
          setModalVisible={setRatingModalVisible}
        />
      </ScrollView>
      {!ratingModalVisible && <FeedbackBottomSheet />}
      {loading && (
        <View
          style={[
            styles.loadingOverlay,
            { backgroundColor: theme.colors.background + '80' },
          ]}
        >
          <ActivityIndicator size="large" color={ypdOldGreen} />
        </View>
      )}
      <SuggestionPopup
        visible={!darkModeModalIntroduced}
        onClose={() => {
          handleDarkModeModal();
        }}
        darkModeModal={true}
        isClose={true}
      />
    </SafeAreaView>
  );
};

export default Settings;

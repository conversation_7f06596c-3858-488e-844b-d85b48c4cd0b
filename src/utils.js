import { useSelector } from 'react-redux';
import Oral from './assets/svgs/profileBuilder/Oral';
import Inhaled from './assets/svgs/profileBuilder/Inhaled';
import CBD from './assets/svgs/profileBuilder/CBD';
import THC from './assets/svgs/profileBuilder/THC';
import * as Keychain from 'react-native-keychain';

// for making dosing values
const generateDoseValues = (start, end, increase = 1, outliers = [], type) => {
  const doseList = [];
  let value = start;

  if (start !== null && end !== null) {
    while (value <= end) {
      doseList.push({
        label: `${value} ${type === 'vape' ? 'puff(s)' : 'mg'}`,
        value: parseFloat(value),
      });
      value += increase;
    }
  }

  if (outliers.length) {
    outliers.forEach((outlier) => {
      const exists = doseList.some(
        (item) => item.value === parseFloat(outlier),
      );
      if (!exists) {
        doseList.push({
          label: `${outlier} ${type === 'vape' ? 'puff(s)' : 'mg'}`,
          value: parseFloat(outlier),
        });
      }
    });
  }

  return doseList.sort((a, b) => a.value - b.value);
};

export const ages = ['18-29', '30-44', '45-59', '60+'];

export const genders = ['Female', 'Male', 'Non-binary/Other'];

export const useUsername = () => {
  return useSelector((state) => state.profile.username);
};

export const useEmail = () => {
  return useSelector((state) => state.profile.email);
};

export const useName = () => {
  return useSelector((state) => state.profile.name);
};

//Fonts
export const Fonts = {
  LIGHT: 'Roboto-Light',
  REGULAR: 'Roboto-Regular',
  MEDIUM: 'Roboto-Medium',
  BOLD: 'Roboto-Bold',
  BLACK: 'Roboto-Black',
};

//Health Concerns
export const healthOptions = [
  'Mental health',
  'Sleep issues',
  'Stress',
  'Pain',
  'Anxiety',
  'Nausea',
];

//Common Uses
export const thcCommonUses = [
  'Works directly',
  'Low doses are effective',
  'Can be psychoactive',
];

export const cbdCommonUses = [
  'Baseline relief ',
  'Decreases THC side effects',
  'Not psychoactive',
];

// Side Effects Options
export const sideEffectOptions = [
  'Buzz',
  'Anxiety',
  'Hunger',
  'Headache',
  'Skin Rash*',
  'Fatigue',
];

export const oralUses = ['Slower onset', 'Lasts for hours'];
export const inhaledUses = ['Works more rapidly', 'Great for flareups'];

// doses values
export const thciData = generateDoseValues(0, 20, 1, [], 'vape');
export const cbdiData = generateDoseValues(0, 30, 1, [], 'vape');
export const thcoData = generateDoseValues(
  0,
  20,
  1,
  [25, 30, 35, 40, 45, 50, 60, 70, 80, 90, 100],
  'oral',
);
export const cbdoData = generateDoseValues(
  0,
  1,
  1,
  [
    5, 10, 15, 20, 25, 50, 75, 100, 150, 200, 250, 300, 400, 500, 600, 700, 800,
    900, 1000,
  ],
  'oral',
);

export const getDoseConfig = (thci, thco, cbdi, cbdo) => {
  return {
    'THC oral': {
      svg: <Oral width={100} height={100} />,
      data: thcoData,
      lastDose: thco,
      heading: 'Oral THC By mouth',
      subHeading: 'Every 2 hours as needed',
      reduxKey: 'OralTHC',
    },
    'THC inhaled': {
      svg: <Inhaled width={100} height={100} />,
      data: thciData,
      lastDose: thci,
      heading: 'Inhaled THC By vape',
      subHeading: 'Every 1 hour as needed',
      reduxKey: 'InhaleTHC',
    },
    'CBD oral': {
      svg: <CBD width={100} height={100} />,
      data: cbdoData,
      lastDose: cbdo,
      heading: 'Oral CBD By mouth',
      subHeading: 'Every 2 hours as needed',
      reduxKey: 'OralCBD',
    },
    'CBD inhaled': {
      svg: <THC width={100} height={100} />,
      data: cbdiData,
      lastDose: cbdi,
      heading: 'Inhaled CBD By vape',
      subHeading: 'Every 1 hour as needed',
      reduxKey: 'InhaleCBD',
    },
  };
};

export const policiesConditions = [
  'I understand that YPD provides suggestions, but the final decision on how I use cannabis is mine alone.',
  'I confirm that I am at least 18 years old and do not have any conditions excluded in the YPD Safety Agreement.',
  'I reside in a U.S. state where medical cannabis use is legal.',
  'I will use the YPD app to support my wellness goals.',
];

import { Dimensions } from 'react-native';
import { useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { resetStore, store } from './redux/store';
import LocalNotifactionService from './utils/localNotifactionService';

export const resetLoadingState = (resetFunction) => {
  useFocusEffect(
    useCallback(() => {
      resetFunction(false);
    }, [resetFunction]),
  );
};
const { width, height } = Dimensions.get('window');

// Device breakpoints
export const isExtraSmallDevice = width <= 360;
export const isSmallDevice = width <= 375;
export const isRegularPhone = width <= 412;
export const isLargePhone = width <= 480;

export const responsiveStyles = {
  // Font size for body text
  fontSize: isExtraSmallDevice
    ? 13
    : isSmallDevice
      ? 15
      : isRegularPhone
        ? 16
        : isLargePhone
          ? 18
          : 20,

  // Image container height
  imageContainerHeight: isExtraSmallDevice
    ? 150
    : isSmallDevice
      ? 170
      : isRegularPhone
        ? 200
        : isLargePhone
          ? 250
          : 300,

  // Floating button size
  buttonPosition: isExtraSmallDevice
    ? height * 0.48
    : isSmallDevice
      ? height * 0.83
      : isRegularPhone
        ? height * 0.7
        : isLargePhone
          ? height * 0.63
          : height * 0.78,

  // Add this to the existing object
  progressBarPaddingTop: isExtraSmallDevice
    ? 70
    : isSmallDevice
      ? 90
      : isRegularPhone
        ? 100
        : isLargePhone
          ? 115
          : 110,
  // Circular slider responsive
  sliderWidth: isExtraSmallDevice
    ? 230
    : isSmallDevice
      ? 250
      : isRegularPhone
        ? 280
        : isLargePhone
          ? 320
          : 360,
  // Svg icons on slider
  svgIconSize: isExtraSmallDevice
    ? 30
    : isSmallDevice
      ? 35
      : isRegularPhone
        ? 40
        : isLargePhone
          ? 45
          : 50,
  modalMarginBottom: isExtraSmallDevice
    ? 40
    : isSmallDevice
      ? 60
      : isRegularPhone
        ? 90
        : isLargePhone
          ? 90
          : 90,
};

export const resetState = async (signOut = null) => {
  try {
    store.dispatch(resetStore());
    await Keychain.resetGenericPassword();
    // await LocalNotifactionService.cancelAllLocalNotifications(); // TODO
    if (signOut) {
      await signOut({ global: true });
    }
    console.log('Local state cleared.');
  } catch (error) {
    console.error('Error during resetting state:', error);
  }
};
